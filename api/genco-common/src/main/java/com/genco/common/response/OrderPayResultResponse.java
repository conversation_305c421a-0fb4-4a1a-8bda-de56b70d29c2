package com.genco.common.response;

import com.genco.common.vo.AliPayJsResultVo;
import com.genco.common.vo.WxPayJsResultVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单支付结果 Response
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OrderPayResultResponse对象", description = "订单支付结果响应对象")
public class OrderPayResultResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "支付状态")
    private Boolean status;

    @ApiModelProperty(value = "支付类型")
    private String payType;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "调起支付参数对象")
    private WxPayJsResultVo jsConfig;

    @ApiModelProperty(value = "订单支付宝支付")
    private String alipayRequest;

    @ApiModelProperty(value = "支付宝调起支付参数对象（app支付专用）")
    private AliPayJsResultVo aliPayConfig;

    @ApiModelProperty(value = "支付链接")
    private String payUrl;

    @ApiModelProperty(value = "二维码")
    private String qrCode;

    @ApiModelProperty(value = "外部支付单号")
    private String outTradeNo;

    @ApiModelProperty(value = "支付渠道")
    private String payChannel;
}
