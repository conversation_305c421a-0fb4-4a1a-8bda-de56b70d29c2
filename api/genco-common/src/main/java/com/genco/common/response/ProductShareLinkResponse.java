package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 商品分享链接响应对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "ProductShareLinkResponse对象", description = "商品分享链接响应对象")
public class ProductShareLinkResponse implements Serializable {

    private static final long serialVersionUID = 8822745472328151094L;

    @ApiModelProperty(value = "产品ID")
    private String productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品图片")
    private String productImageUrl;

    @ApiModelProperty(value = "链接渠道")
    private String channel;

    @ApiModelProperty(value = "分享链接")
    private String shareLink;

    @ApiModelProperty(value = "分享链接")
    private String tags;
}
