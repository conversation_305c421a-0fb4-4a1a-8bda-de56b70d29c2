package com.genco.admin.controller;

import com.genco.common.request.AffiliateProductSearchRequest;
import com.genco.common.response.AffiliateProductResponse;
import com.genco.common.response.CommonResult;
import com.genco.service.service.AffiliateProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 联盟产品控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/affiliate/products")
@Api(tags = "运营中心 - 联盟产品")
public class AffiliateProductController {

    @Autowired
    private AffiliateProductService affiliateProductService;

    /**
     * 搜索联盟产品
     */
    @ApiOperation(value = "搜索联盟产品", notes = "基于TikTok Shop API的联盟产品搜索功能")
    @PostMapping("/search")
    public CommonResult<AffiliateProductResponse> searchProducts(@RequestBody @Validated AffiliateProductSearchRequest request) {

        log.info("联盟产品搜索请求 - 页面大小: {}, 关键词: {}, 类目: {}",
                request.getPageSize(), request.getTitleKeywords(), request.getCategoryId());

        try {
            // 调用Service层进行搜索
            log.info("调用AffiliateProductService进行联盟产品搜索");
            AffiliateProductResponse response = affiliateProductService.searchProducts(request);

            log.info("联盟产品搜索成功，返回产品数量: {}",
                response.getProducts() != null ? response.getProducts().size() : 0);

            // 返回搜索结果
            return CommonResult.success(response, "联盟产品搜索成功");

        } catch (Exception e) {
            log.error("联盟产品搜索失败", e);

            // 根据异常类型返回不同的错误信息
            String errorMessage = e.getMessage();
            if (errorMessage != null) {
                if (errorMessage.contains("timeout") || errorMessage.contains("timed out")) {
                    return CommonResult.failed("TikTok API请求超时，请稍后重试");
                } else if (errorMessage.contains("401") || errorMessage.contains("Unauthorized")) {
                    return CommonResult.failed("TikTok API认证失败，请检查访问令牌");
                } else if (errorMessage.contains("403") || errorMessage.contains("Forbidden")) {
                    return CommonResult.failed("TikTok API权限不足，请检查应用权限配置");
                } else if (errorMessage.contains("429") || errorMessage.contains("rate limit")) {
                    return CommonResult.failed("TikTok API调用频率超限，请稍后重试");
                } else if (errorMessage.contains("配置")) {
                    return CommonResult.failed("TikTok API配置错误: " + errorMessage);
                }
            }

            return CommonResult.failed("联盟产品搜索失败: " + errorMessage);
        }
    }




}
