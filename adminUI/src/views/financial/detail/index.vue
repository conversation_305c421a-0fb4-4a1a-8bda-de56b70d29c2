<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <el-tabs
        v-model="tableFromType"
        @tab-click="getList(tableFromType, 1)"
        class="mb20"
      >
        <el-tab-pane
          :label="$t('financial.detail.purchaseDetail')"
          name="purchase"
        ></el-tab-pane>
        <el-tab-pane
          :label="$t('financial.detail.tradeDetail')"
          name="trade"
        ></el-tab-pane>
      </el-tabs>
      <div class="container mt-1">
        <el-form
          v-if="tableFromType === 'purchase'"
          v-model="purchaseFrom"
          inline
          size="small"
        >
          <el-form-item :label="$t('financial.detail.rechargeType') + '：'">
            <el-select
              v-model="purchaseFrom.rechargeType"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                v-for="(item, index) in rechargeTypeList"
                :key="index"
                :label="$t('financial.detail.' + item.label)"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('financial.detail.transactionTime') + '：'">
            <el-date-picker
              v-model="timeList"
              :placeholder="$t('common.startDate')"
              clearable
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              size="small"
              type="daterange"
              placement="bottom-end"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
              style="width: 250px;"
            />
          </el-form-item>
          <el-form-item :label="$t('financial.detail.paymentMethod') + '：'">
            <el-select
              v-model="purchaseFrom.payChannel"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                :label="$t('financial.detail.bankTransfer')"
                value="xendit"
              ></el-option>
              <el-option
                :label="$t('financial.detail.electronicWallet')"
                value="haipay"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('financial.detail.electronicWallet') + '：'"
            v-if="purchaseFrom.payChannel == 'haipay'"
          >
            <el-select
              v-model="purchaseFrom.walletCode"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                v-for="item in walletList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            :label="$t('financial.detail.bankName') + '：'"
            v-if="purchaseFrom.payChannel == 'xendit'"
          >
            <el-select
              v-model="purchaseFrom.bankName"
              clearable
              :placeholder="$t('common.all')"
            >
              <el-option
                v-for="(item, index) in bankList"
                :key="index"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <el-form v-else v-model="tradeFrom" inline size="small">
          <el-form-item :label="$t('financial.detail.tradeNo') + '：'">
            <el-input
              v-model="tradeFrom.linkId"
              size="small"
              :placeholder="$t('financial.detail.tradeNo')"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('financial.detail.transactionTime') + '：'">
            <el-date-picker
              v-model="timeList"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              size="small"
              type="daterange"
              placement="bottom-end"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
              style="width: 250px;"
              clearable
            />
          </el-form-item>
          <el-form-item :label="$t('financial.detail.tradeType') + '：'">
            <el-select
              v-model="tradeFrom.type"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                :label="$t('financial.detail.agentFee')"
                value="1"
              ></el-option>
              <el-option
                :label="$t('financial.detail.partnerFee')"
                value="2"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <el-button
        size="small"
        type="primary"
        class="mr10"
        @click="getList(tableFromType)"
        >{{ $t("common.query") }}</el-button
      >

      <el-button size="small" type="" class="mr10" @click="handleReset()">{{
        $t("common.reset")
      }}</el-button>
    </el-card>
    <el-card class="box-card" style="margin-top: 12px;">
      <div slot="header" class="clearfix">
        <el-button
          type="primary"
          size="small"
          @click="handleUpload"
          v-hasPermi="['admin:financialCenter:detail:upload']"
          >{{ $t("financial.detail.exportExcel") }}</el-button
        >
      </div>
      <el-table
        v-if="tableFromType === 'purchase'"
        v-loading="loading"
        :data="purchaseTableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          type="index"
          :label="$t('common.serialNumber')"
          width="110"
        >
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.paymentTime')"
          min-width="150"
        >
          <template slot-scope="scope">{{
            scope.row.payTime | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.paymentNo')"
          min-width="150"
        >
          <template slot-scope="scope">{{
            scope.row.outTradeNo | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.rechargeType')"
          min-width="80"
        >
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.paymentAccount')"
          min-width="80"
        >
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.tradeAmount')"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.actualPaymentAmount')"
          min-width="80"
        >
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.paymentMethod')"
          min-width="100"
          prop="payChannel"
        >
          <template slot-scope="scope">{{
            scope.row.payChannel | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.electronicWallet')"
          min-width="80"
        >
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.institutionNumber')"
          min-width="80"
        >
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.bankName')"
          min-width="80"
        >
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.paymentAccount')"
          min-width="80"
        >
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.mobile')"
          min-width="100"
          prop="phone"
        >
          <template slot-scope="scope">{{
            scope.row.phone | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column :label="$t('financial.detail.payee')" min-width="80">
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.payeeAccount')"
          min-width="80"
        >
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="tableFromType === 'purchase'"
        class="mt20"
        @size-change="e => sizeChange(e, 'purchase')"
        @current-change="e => pageChange(e, 'purchase')"
        :current-page="purchaseFrom.page"
        :page-sizes="[20, 40, 60, 100]"
        :page-size="purchaseFrom.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="purchaseFrom.total"
      >
      </el-pagination>
      <el-table
        v-if="tableFromType === 'trade'"
        v-loading="loading"
        :data="tradeTableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          type="index"
          :label="$t('common.serialNumber')"
          width="110"
        >
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.tradeNo')"
          min-width="150"
        >
          <template slot-scope="scope">{{
            scope.row.linkId | filterEmpty
          }}</template></el-table-column
        >
        <el-table-column
          :label="$t('financial.detail.tradeType')"
          min-width="80"
        >
          <template slot-scope="scope">{{
            scope.row.title | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.detail.tradeAmount')"
          min-width="100"
        >
          <template slot-scope="scope">{{
            scope.row.number | filterEmpty
          }}</template></el-table-column
        >
        <el-table-column
          :label="$t('financial.detail.transactionTime')"
          min-width="150"
        >
          <template slot-scope="scope">{{
            scope.row.createTime | filterEmpty
          }}</template></el-table-column
        >
        <el-table-column
          :label="$t('financial.detail.userNickname')"
          min-width="80"
        >
          <template slot-scope="scope">{{
            scope.row.nickName | filterEmpty
          }}</template></el-table-column
        >
        <el-table-column
          :label="$t('financial.detail.tikTokAccount')"
          min-width="80"
        >
          <template slot-scope="scope">{{
            scope.row.tiktokAccount | filterEmpty
          }}</template></el-table-column
        >
        <el-table-column
          :label="$t('financial.detail.whatsApp')"
          min-width="80"
        >
          <template slot-scope="scope">{{
            scope.row.whatsAppAccount | filterEmpty
          }}</template></el-table-column
        >
        <el-table-column :label="$t('financial.detail.channel')" min-width="80">
          <template slot-scope="scope">{{
            scope.row.channel | filterEmpty
          }}</template></el-table-column
        >
        <el-table-column
          :label="$t('financial.detail.orderNo')"
          min-width="150"
        >
          <template slot-scope="scope">{{
            scope.row.linkId | filterEmpty
          }}</template></el-table-column
        >
      </el-table>
      <el-pagination
        v-if="tableFromType === 'trade'"
        class="mt20"
        @size-change="e => sizeChange(e, 'trade')"
        @current-change="e => pageChange(e, 'trade')"
        :current-page="tradeFrom.page"
        :page-sizes="[20, 40, 60, 100]"
        :page-size="tradeFrom.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tradeFrom.total"
      >
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import {
  topUpLogListsApi,
  fundsMonitorListApi,
  extractBankApi
} from "@/api/financial";
export default {
  name: "FinancialDetail",
  data() {
    return {
      loading: false,
      purchaseTableData: [],
      tradeTableData: [],
      timeList: [],
      purchaseFrom: {
        uid: "",
        keyword: "",
        rechargeType: "",
        dateLimit: [],
        payChannel: "",
        walletCode: "",
        bankName: "",
        page: 1,
        limit: 20,
        total: 0
      },
      tradeFrom: {
        keyword: "",
        dateLimit: [],
        linkId: "",
        type: "",
        page: 1,
        limit: 20,
        total: 0
      },
      tableFromType: "purchase",
      rechargeTypeList: [
        { label: "agentFee", value: "agent" },
        { label: "partnerFee", value: "partner" }
      ],
      walletList: [
        { label: "ShopeePay", value: "ShopeePay" },
        { label: "DANA", value: "DANA" },
        { label: "OVO", value: "OVO" },
        { label: "Gopay", value: "Gopay" }
      ],
      bankList: []
    };
  },
  created() {},
  mounted() {
    this.getList(this.tableFromType);
    this.getBankList();
  },
  methods: {
    // 获取银行列表
    getBankList() {
      extractBankApi()
        .then(res => {
          this.bankList = res;
        })
        .catch(() => {});
    },
    getList(type, num) {
      this.loading = true;
      if (type === "purchase") {
        this.purchaseFrom.page = num ? num : this.purchaseFrom.page;
        this.purchaseFrom.dateLimit = this.timeList.length
          ? this.timeList.join(",")
          : "";
        topUpLogListsApi(this.purchaseFrom).then(res => {
          this.purchaseTableData = res.list;
          this.purchaseFrom.total = res.total;
          this.loading = false;
        });
      } else {
        this.tradeFrom.page = num ? num : this.tradeFrom.page;
        this.tradeFrom.dateLimit = this.timeList.length
          ? this.timeList.join(",")
          : "";
        fundsMonitorListApi(this.tradeFrom).then(res => {
          this.tradeTableData = res.list;
          this.tradeFrom.total = res.total;
          this.loading = false;
        });
      }
    },
    //切换页数
    pageChange(index, type) {
      if (type === "purchase") {
        this.purchaseFrom.page = index;
      } else {
        this.tradeFrom.page = index;
      }
    },
    //切换显示条数
    sizeChange(index, type) {
      if (type === "purchase") {
        this.purchaseFrom.limit = index;
      } else {
        this.tradeFrom.limit = index;
      }
      this.getList(type);
    },
    handleReset() {
      this.purchaseFrom = {
        uid: "",
        keyword: "",
        rechargeType: "",
        dateLimit: [],
        payChannel: "",
        walletCode: "",
        bankName: "",
        page: 1,
        limit: 20,
        total: 0
      };
      this.tradeFrom = {
        keyword: "",
        dateLimit: [],
        linkId: "",
        type: "",
        page: 1,
        limit: 20,
        total: 0
      };
    },
    handleUpload() {}
  }
};
</script>
<style scoped lang="scss">
/**/
</style>
