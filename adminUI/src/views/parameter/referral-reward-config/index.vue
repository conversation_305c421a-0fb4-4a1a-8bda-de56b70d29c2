<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        size="mini"
        :highlight-current-row="true"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          type="index"
          :label="$t('common.serialNumber')"
          width="110"
        >
        </el-table-column>
        <el-table-column
          prop="id"
          :label="$t('parameter.referralRewardConfig.rewardTemplateId')"
          min-width="120"
        />
        <el-table-column
          prop="name"
          :label="$t('parameter.referralRewardConfig.rewardTemplateName')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="value1"
          :label="$t('parameter.referralRewardConfig.referralCount')"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="value2"
          :label="$t('parameter.referralRewardConfig.firstOrderCount')"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="value3"
          :label="$t('parameter.referralRewardConfig.rewardAmount')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          :label="$t('parameter.referralRewardConfig.operation')"
          min-width="100"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              size="small"
              type="text"
              @click="handleEdit(scope.row)"
              >{{ $t("parameter.referralRewardConfig.edit") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        append-to-body
        :visible.sync="dialogFormVisible"
        :title="$t('parameter.referralRewardConfig.editTitle')"
        width="750px"
        @close="handleCancle"
      >
        <el-form
          ref="elForm"
          inline
          :model="artFrom"
          :rules="rules"
          label-width="200px"
        >
          <el-form-item
            :label="$t('parameter.referralRewardConfig.rewardTemplateId')"
            prop="id"
          >
            <el-input
              v-model="artFrom.id"
              size="small"
              :disabled="true"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('parameter.referralRewardConfig.rewardTemplateName')"
            prop="name"
          >
            <el-input
              v-model="artFrom.name"
              size="small"
              :disabled="true"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('parameter.referralRewardConfig.referralCount')"
            prop="value1"
          >
            <el-input
              v-model="artFrom.value1"
              size="small"
              type="number"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('parameter.referralRewardConfig.firstOrderCount')"
            prop="value2"
          >
            <el-input
              v-model="artFrom.value2"
              size="small"
              type="number"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('parameter.referralRewardConfig.rewardAmount')"
            prop="value3"
          >
            <el-input
              v-model="artFrom.value3"
              size="small"
              type="number"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button type="primary" @click="handelConfirm">{{
            $t("common.confirm")
          }}</el-button>
          <el-button @click="handleCancle">{{ $t("common.cancel") }}</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { configInfo, configSaveForm } from "@/api/parameter";
export default {
  name: "ReferralRewardConfig",
  data() {
    return {
      loading: false,
      tableData: [],
      dialogFormVisible: false,
      artFrom: {
        id: "",
        name: "",
        value1: "",
        value2: "",
        value3: ""
      },
      rules: {
        value1: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          },
          {
            validator: this.validateReferralCount,
            trigger: "blur"
          }
        ],
        value2: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          },
          {
            validator: this.validateFirstOrderCount,
            trigger: "blur"
          }
        ],
        value3: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          },
          {
            validator: this.validateRewardAmount,
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {},
  mounted() {
    this.getList();
  },
  methods: {
    // 验证拉新数
    validateReferralCount(rule, value, callback) {
      if (value < 0) {
        callback(new Error(this.$t('parameter.referralRewardConfig.validation.referralCountMin')));
      } else {
        callback();
      }
    },
    // 验证首单数
    validateFirstOrderCount(rule, value, callback) {
      if (value < 0) {
        callback(new Error(this.$t('parameter.referralRewardConfig.validation.firstOrderCountMin')));
      } else if (parseInt(value) >= parseInt(this.artFrom.value1)) {
        callback(new Error(this.$t('parameter.referralRewardConfig.validation.firstOrderCountMax')));
      } else {
        callback();
      }
    },
    // 验证奖励金
    validateRewardAmount(rule, value, callback) {
      if (value < 0) {
        callback(new Error(this.$t('parameter.referralRewardConfig.validation.rewardAmountMin')));
      } else {
        callback();
      }
    },
    // 列表
    getList() {
      this.tableData = [];
      this.loading = true;
      // 使用formId 144来获取拉新奖励配置数据
      configInfo({ formId: "144" }).then(res => {
        if (res) {
          this.tableData.push({
            id: res.id,
            name: res.template_name,
            value1: res.referral_count,
            value2: res.first_order_count,
            value3: res.reward_amount
          });
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 编辑
    handleEdit(row) {
      this.artFrom = {
        id: row.id,
        name: row.name,
        value1: row.value1,
        value2: row.value2,
        value3: row.value3
      };
      this.dialogFormVisible = true;
    },
    // 确认
    handelConfirm() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return;

        // 构建保存参数
        let param = {
          id: this.artFrom.id,
          sort: 1,
          status: true,
          fields: [
            {
              name: "template_name",
              value: this.artFrom.name,
              title: "template_name"
            },
            {
              name: "referral_count",
              value: this.artFrom.value1,
              title: "referral_count"
            },
            {
              name: "first_order_count",
              value: this.artFrom.value2,
              title: "first_order_count"
            },
            {
              name: "reward_amount",
              value: this.artFrom.value3,
              title: "reward_amount"
            }
          ]
        };

        configSaveForm(param).then(() => {
          this.$message.success(this.$t("common.success"));
          this.dialogFormVisible = false;
          this.getList();
        }).catch(() => {
          this.$message.error(this.$t("common.failed"));
        });
      });
    },
    // 取消
    handleCancle() {
      this.dialogFormVisible = false;
      this.artFrom = {
        id: "",
        name: "",
        value1: "",
        value2: "",
        value3: ""
      };
    }
  }
};
</script>

<style scoped>
.divBox {
  padding: 20px;
}
</style>
