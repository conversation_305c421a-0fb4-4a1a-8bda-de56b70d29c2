import request from '@/utils/request'

/**
 * TikTok Shop API相关接口
 */

/**
 * 联盟选品查询（新版本）
 * @param {Object} params 查询参数
 * @param {Number} params.pageSize 每页数量
 * @param {String} params.cursor 分页游标
 * @param {String} params.sortField 排序字段
 * @param {String} params.sortOrder 排序方向
 * @param {Array} params.titleKeywords 标题关键词
 * @param {String} params.salesPriceMin 最低价格
 * @param {String} params.salesPriceMax 最高价格
 * @param {Number} params.commissionRateMin 最低佣金率
 * @param {Number} params.commissionRateMax 最高佣金率
 * @param {String} params.categoryId 分类ID
 */
export function searchAffiliateProducts(params) {
  const requestBody = {
    pageSize: params.pageSize || 20,
    cursor: params.cursor || null,
    sortField: params.sortField || 'commission_rate',
    sortOrder: params.sortOrder || 'DESC'
  }

  // 添加可选参数
  if (params.titleKeywords && params.titleKeywords.length > 0) {
    requestBody.titleKeywords = params.titleKeywords
  }
  if (params.salesPriceMin) {
    requestBody.salesPriceMin = params.salesPriceMin
  }
  if (params.salesPriceMax) {
    requestBody.salesPriceMax = params.salesPriceMax
  }
  if (params.commissionRateMin) {
    requestBody.commissionRateMin = params.commissionRateMin
  }
  if (params.commissionRateMax) {
    requestBody.commissionRateMax = params.commissionRateMax
  }
  if (params.categoryId) {
    requestBody.categoryId = params.categoryId
  }

  return request({
    url: '/admin/affiliate/products/search',
    method: 'post',
    data: requestBody
  })
}
